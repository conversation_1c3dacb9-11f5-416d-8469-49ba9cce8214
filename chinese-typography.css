/*
 * File: chinese-typography.css
 * Description: 此CSS片段用于优化Obsidian的中文排版，
 *              实现段落两端对齐、首行缩进，
 *              并为当前编辑段落添加左侧棕色标记线。
 * Author: 根据用户需求定制
 * Version: 1.0
 */

/* ==================================== */
/* 1. 段落排版样式 (Paragraph Typography) */
/* ==================================== */

/*
 * 为阅读模式下的标准段落 <p> 标签应用样式。
 * 确保文本左右两端对齐，并首行缩进两个中文字符。
 * text-align: justify 适用于多种语言，能使文本块更整洁。
 * text-indent: 2em; 使用相对单位 em，确保缩进量始终等于两个字符的宽度，
 * 无论字体大小如何变化。
 */
.markdown-reading-view p {
  text-align: justify;
  text-indent: 2em;
  line-height: 1.6; /* 增加行间距，提升阅读体验 */
}

/*
 * 为实时预览模式下的文本行 div.cm-line 应用相同样式。
 * 这确保了在编辑时也能看到相同的排版效果。
 * 注意，此模式下没有标准 <p> 标签，因此需要针对每一行进行调整。
 */
.markdown-source-view.is-live-preview div.cm-line {
  text-align: justify;
  text-indent: 2em;
  line-height: 1.6;
}

/*
 * 特殊处理：确保列表项、标题等不受首行缩进影响
 * 这些元素通常不需要首行缩进
 */
.markdown-reading-view h1,
.markdown-reading-view h2,
.markdown-reading-view h3,
.markdown-reading-view h4,
.markdown-reading-view h5,
.markdown-reading-view h6,
.markdown-reading-view ul,
.markdown-reading-view ol,
.markdown-reading-view blockquote {
  text-indent: 0;
}

.markdown-source-view.is-live-preview .cm-header,
.markdown-source-view.is-live-preview .cm-list,
.markdown-source-view.is-live-preview .cm-quote {
  text-indent: 0;
}

/* ==================================== */
/* 2. 光标焦点标记 (Active Line Marker)  */
/* ==================================== */

/*
 * 为当前光标所在的行.cm-active 设置相对定位。
 * 这是为了让 ::before 伪元素能够相对于此行进行绝对定位。
 */
.markdown-source-view.is-live-preview div.cm-active {
  position: relative;
}

/*
 * 使用 ::before 伪元素在当前光标行的左侧创建一条棕色垂直线。
 * content: '' 用于生成伪元素。
 * position: absolute; 使其脱离文档流，进行精确定位。
 * top: 0; bottom: 0; height: 100%; 确保线条从行顶延伸至行底。
 * left: -10px; 控制线条的水平位置，稍微向左偏移避免遮挡文字。
 * width: 3px; 设定线条的粗细。
 * background-color: #8B4513; 设置线条为马鞍棕色，可根据喜好修改。
 * border-radius: 2px; 添加圆角，使线条更加美观。
 */
.markdown-source-view.is-live-preview div.cm-active::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: -10px;
  height: 100%;
  width: 3px;
  background-color: #8B4513; /* 马鞍棕色 */
  border-radius: 2px;
  opacity: 0.8; /* 添加透明度，使效果更柔和 */
  transition: opacity 0.2s ease; /* 添加过渡效果 */
}

/*
 * 可选：为活跃行添加轻微的背景高亮
 * 如果不需要背景高亮，可以删除此规则
 */
.markdown-source-view.is-live-preview div.cm-active {
  background-color: rgba(139, 69, 19, 0.05); /* 非常淡的棕色背景 */
  border-radius: 3px;
  transition: background-color 0.2s ease;
}

/* ==================================== */
/* 3. 额外优化 (Additional Enhancements) */
/* ==================================== */

/*
 * 优化段落间距，使排版更加美观
 */
.markdown-reading-view p {
  margin-bottom: 1em;
}

/*
 * 确保在编辑模式下也有合适的段落间距
 */
.markdown-source-view.is-live-preview .cm-line {
  margin-bottom: 0.2em;
}

/*
 * 为代码块和引用块保持原有的对齐方式
 */
.markdown-reading-view pre,
.markdown-reading-view code,
.markdown-reading-view blockquote {
  text-align: left;
  text-indent: 0;
}

.markdown-source-view.is-live-preview .cm-line.HyperMD-codeblock,
.markdown-source-view.is-live-preview .cm-line.HyperMD-quote {
  text-align: left;
  text-indent: 0;
}

/* ==================================== */
/* 4. 自定义选项 (Customization Options) */
/* ==================================== */

/*
 * 如需调整缩进量，修改下面的数值：
 * - 1em = 1个字符宽度
 * - 1.5em = 1.5个字符宽度
 * - 2em = 2个字符宽度（当前设置）
 * - 2.5em = 2.5个字符宽度
 */

/*
 * 如需调整标记线颜色，可选择以下颜色之一：
 * - #8B4513 (马鞍棕色，当前使用)
 * - #A0522D (赭色)
 * - #CD853F (秘鲁色)
 * - #D2691E (巧克力色)
 * - #B8860B (深金黄色)
 */

/*
 * 如需调整标记线宽度，修改 width 属性：
 * - 2px (较细)
 * - 3px (当前设置)
 * - 4px (较粗)
 * - 5px (很粗)
 */
