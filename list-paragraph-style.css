/*
 * File: list-paragraph-style.css
 * Description: 此CSS片段用于让Obsidian中的有序列表和无序列表
 *              像普通段落一样显示，取消第二行开始的额外缩进，
 *              实现更整齐的文本对齐效果。
 * Author: 根据用户需求定制
 * Version: 1.0
 */

/* ==================================== */
/* 1. 阅读模式下的列表样式重置 */
/* ==================================== */

/*
 * 重置有序列表和无序列表的默认样式
 * 取消默认的左侧内边距和外边距
 */
.markdown-reading-view ul,
.markdown-reading-view ol {
  padding-left: 0;
  margin-left: 0;
  list-style-position: outside; /* 确保列表标记在外部 */
}

/*
 * 重置列表项的样式
 * 取消默认的缩进，让文本从左边缘开始
 */
.markdown-reading-view li {
  margin-left: 0;
  padding-left: 0;
  text-indent: 0; /* 确保没有首行缩进 */
  position: relative; /* 为自定义标记做准备 */
}

/*
 * 隐藏默认的列表标记（圆点、数字等）
 * 我们将用自定义的方式来显示
 */
.markdown-reading-view ul li {
  list-style-type: none;
}

.markdown-reading-view ol li {
  list-style-type: none;
}

/*
 * 为无序列表项添加自定义的圆点标记
 * 使用 ::before 伪元素在行首添加标记
 */
.markdown-reading-view ul li::before {
  content: "• "; /* 使用圆点符号 */
  color: var(--text-muted); /* 使用主题的次要文本颜色 */
  font-weight: bold;
  position: absolute;
  left: -1.2em; /* 将标记放在文本左侧 */
  top: 0;
}

/*
 * 为有序列表项添加自定义的数字标记
 * 使用 counter 来自动生成数字
 */
.markdown-reading-view ol {
  counter-reset: list-counter; /* 重置计数器 */
}

.markdown-reading-view ol li {
  counter-increment: list-counter; /* 递增计数器 */
}

.markdown-reading-view ol li::before {
  content: counter(list-counter) ". "; /* 显示数字和点 */
  color: var(--text-muted);
  font-weight: bold;
  position: absolute;
  left: -2em; /* 为数字留出更多空间 */
  top: 0;
  min-width: 1.5em; /* 确保数字对齐 */
  text-align: right; /* 右对齐数字 */
}

/*
 * 确保列表项的文本内容从左边缘开始
 * 不受列表标记影响
 */
.markdown-reading-view li p {
  margin: 0;
  padding: 0;
  text-indent: 0;
}

/* ==================================== */
/* 2. 实时预览模式下的列表样式重置 */
/* ==================================== */

/*
 * 在实时预览模式下，列表的结构更复杂
 * 需要针对 CodeMirror 的特定类进行调整
 */

/*
 * 重置列表行的缩进
 */
.markdown-source-view.is-live-preview .cm-line.HyperMD-list-line {
  text-indent: 0;
  padding-left: 0;
}

/*
 * 隐藏默认的列表标记
 */
.markdown-source-view.is-live-preview .cm-formatting-list {
  opacity: 0.3; /* 让原始标记变淡而不是完全隐藏 */
}

/*
 * 调整列表内容的对齐
 */
.markdown-source-view.is-live-preview .cm-list {
  margin-left: 0;
  padding-left: 0;
}

/* ==================================== */
/* 3. 嵌套列表的处理 */
/* ==================================== */

/*
 * 处理嵌套列表的缩进
 * 每一级嵌套增加适当的左边距
 */
.markdown-reading-view ul ul,
.markdown-reading-view ol ol,
.markdown-reading-view ul ol,
.markdown-reading-view ol ul {
  margin-left: 1.5em; /* 嵌套列表的缩进 */
  margin-top: 0.2em;
  margin-bottom: 0.2em;
}

/*
 * 嵌套列表项的标记调整
 */
.markdown-reading-view ul ul li::before {
  content: "◦ "; /* 二级列表使用空心圆点 */
  left: -1.2em;
}

.markdown-reading-view ul ul ul li::before {
  content: "▪ "; /* 三级列表使用小方块 */
  left: -1.2em;
}

/* ==================================== */
/* 4. 列表与段落的统一样式 */
/* ==================================== */

/*
 * 让列表项的文本样式与普通段落保持一致
 * 包括两端对齐和行间距
 */
.markdown-reading-view li {
  text-align: justify; /* 两端对齐 */
  line-height: 1.6; /* 与段落相同的行间距 */
  margin-bottom: 0.5em; /* 列表项之间的间距 */
}

/*
 * 确保列表项内的段落样式正确
 */
.markdown-reading-view li p {
  text-align: justify;
  line-height: 1.6;
  margin-bottom: 0.3em;
}

/*
 * 在实时预览模式下也应用相同的样式
 */
.markdown-source-view.is-live-preview .cm-line.HyperMD-list-line {
  text-align: justify;
  line-height: 1.6;
}

/* ==================================== */
/* 5. 特殊情况处理 */
/* ==================================== */

/*
 * 处理列表项中的代码块和引用
 * 这些元素应该保持原有的对齐方式
 */
.markdown-reading-view li code,
.markdown-reading-view li pre,
.markdown-reading-view li blockquote {
  text-align: left;
}

/*
 * 确保列表项中的链接和强调文本正常显示
 */
.markdown-reading-view li a,
.markdown-reading-view li strong,
.markdown-reading-view li em {
  text-decoration: inherit;
  font-weight: inherit;
  font-style: inherit;
}

/* ==================================== */
/* 6. 自定义选项说明 */
/* ==================================== */

/*
 * 自定义列表标记符号：
 * 无序列表：
 * - "• " (实心圆点，当前使用)
 * - "○ " (空心圆点)
 * - "▪ " (小方块)
 * - "→ " (箭头)
 * - "- " (短横线)
 * 
 * 有序列表：
 * - counter(list-counter) ". " (数字+点，当前使用)
 * - counter(list-counter) ") " (数字+右括号)
 * - counter(list-counter, upper-roman) ". " (大写罗马数字)
 * - counter(list-counter, lower-alpha) ". " (小写字母)
 */

/*
 * 调整标记位置：
 * 修改 left 属性的值来调整标记与文本的距离
 * - 负值：标记在文本左侧
 * - 正值：标记在文本右侧（不推荐）
 * 
 * 调整列表项间距：
 * 修改 margin-bottom 属性来调整列表项之间的间距
 */

/*
 * 如果希望完全移除列表标记，可以将以下规则取消注释：
 */
/*
.markdown-reading-view ul li::before,
.markdown-reading-view ol li::before {
  display: none;
}
*/
