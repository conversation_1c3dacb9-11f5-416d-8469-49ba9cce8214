/* Obsidian 自定义文本颜色 CSS 片段 */
/* 根据指南创建，实现粗体红色、斜体黄色、删除线灰色、下划线紫色 */

/* ===== 粗体字：红色 ===== */
/* 使用 Obsidian 内置变量全局设置粗体文本颜色 */
body {
    --bold-color: red;
}

/* ===== 斜体字：黄色 ===== */
/* 使用 Obsidian 内置变量全局设置斜体文本颜色 */
body {
    --italic-color: yellow;
}

/* ===== 删除线字体：文本灰色，删除线黑色 ===== */
/* 适用于阅读视图 */
.markdown-preview-view s,
.markdown-preview-view del {
    color: gray; /* 文本颜色设为灰色 */
    text-decoration-color: black; /* 删除线颜色设为黑色 */
}

/* 适用于实时预览/源码视图 */
.cm-s-obsidian span.cm-strikethrough {
    color: gray; /* 文本颜色设为灰色 */
    text-decoration-color: black; /* 删除线颜色设为黑色 */
}

/* ===== 下划线字体：文本紫色，下划线红色 ===== */
/* 适用于阅读视图 */
.markdown-preview-view u {
    color: purple; /* 文本颜色设为紫色 */
    text-decoration-color: red; /* 下划线颜色设为红色 */
}

/* 适用于实时预览/源码视图 */
.cm-s-obsidian u {
    color: purple; /* 文本颜色设为紫色 */
    text-decoration-color: red; /* 下划线颜色设为红色 */
}

/* ===== 备用选择器（如果上述选择器不生效） ===== */
/* 实时预览模式的备用选择器 */
.markdown-source-view.mod-cm6 .cm-strikethrough {
    color: gray;
    text-decoration-color: black;
}

.markdown-source-view.mod-cm6 u {
    color: purple;
    text-decoration-color: red;
}

/* 如果需要更强的优先级，可以取消注释以下规则并添加 !important */
/*
.markdown-preview-view s,
.markdown-preview-view del {
    color: gray !important;
    text-decoration-color: black !important;
}

.markdown-preview-view u {
    color: purple !important;
    text-decoration-color: red !important;
}
*/

